using UnityEngine;
using System.Collections;

/// <summary>
/// Memory management script specifically for macOS to prevent Asset Garbage Collector crashes
/// </summary>
public class MacOSMemoryManager : MonoBehaviour
{
    [Header("Memory Management Settings")]
    [SerializeField] private float gcInterval = 30f; // Force GC every 30 seconds
    [SerializeField] private bool enablePeriodicGC = true;
    [SerializeField] private bool enableMemoryLogging = false;
    
    private Coroutine gcCoroutine;
    
    void Start()
    {
        // Only run on macOS
        #if UNITY_STANDALONE_OSX && !UNITY_EDITOR
        if (enablePeriodicGC)
        {
            gcCoroutine = StartCoroutine(PeriodicGarbageCollection());
        }
        
        // Set conservative quality settings for macOS
        ApplyMacOSOptimizations();
        
        if (enableMemoryLogging)
        {
            InvokeRepeating(nameof(LogMemoryUsage), 10f, 10f);
        }
        #endif
    }
    
    void OnDestroy()
    {
        if (gcCoroutine != null)
        {
            StopCoroutine(gcCoroutine);
        }
    }
    
    private IEnumerator PeriodicGarbageCollection()
    {
        while (true)
        {
            yield return new WaitForSeconds(gcInterval);
            
            // Force garbage collection to prevent buildup
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            System.GC.Collect();
            
            // Unload unused assets
            yield return Resources.UnloadUnusedAssets();
            
            if (enableMemoryLogging)
            {
                Debug.Log($"[MacOSMemoryManager] Periodic GC completed. Memory: {GetMemoryUsageMB():F1} MB");
            }
        }
    }
    
    private void ApplyMacOSOptimizations()
    {
        // Set conservative quality settings
        QualitySettings.vSyncCount = 1; // Enable VSync to reduce GPU load
        Application.targetFrameRate = 60; // Cap framerate
        
        // Reduce shadow quality if needed
        if (QualitySettings.shadows == ShadowQuality.All)
        {
            QualitySettings.shadowResolution = ShadowResolution.Medium;
        }
        
        Debug.Log("[MacOSMemoryManager] macOS optimizations applied");
    }
    
    private void LogMemoryUsage()
    {
        float memoryMB = GetMemoryUsageMB();
        Debug.Log($"[MacOSMemoryManager] Memory Usage: {memoryMB:F1} MB");
        
        // Warning if memory usage is high
        if (memoryMB > 1000f) // 1GB threshold
        {
            Debug.LogWarning($"[MacOSMemoryManager] High memory usage detected: {memoryMB:F1} MB");
        }
    }
    
    private float GetMemoryUsageMB()
    {
        return (System.GC.GetTotalMemory(false) / 1024f / 1024f);
    }
    
    /// <summary>
    /// Manually trigger garbage collection and asset cleanup
    /// </summary>
    public void ForceCleanup()
    {
        StartCoroutine(ForceCleanupCoroutine());
    }
    
    private IEnumerator ForceCleanupCoroutine()
    {
        Debug.Log("[MacOSMemoryManager] Force cleanup started...");
        
        // Force GC
        System.GC.Collect();
        System.GC.WaitForPendingFinalizers();
        System.GC.Collect();
        
        // Unload unused assets
        yield return Resources.UnloadUnusedAssets();
        
        Debug.Log("[MacOSMemoryManager] Force cleanup completed");
    }
}
