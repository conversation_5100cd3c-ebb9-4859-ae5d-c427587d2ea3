using UnityEngine;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;

/// <summary>
/// Build processor to fix macOS-specific issues including Asset Garbage Collector crashes
/// </summary>
public class MacOSBuildFix : IPreprocessBuildWithReport, IPostprocessBuildWithReport
{
    public int callbackOrder => 0;

    public void OnPreprocessBuild(BuildReport report)
    {
        if (report.summary.platform == BuildTarget.StandaloneOSX)
        {
            Debug.Log("[MacOSBuildFix] Applying macOS-specific build settings...");
            
            // Disable incremental GC for macOS builds to prevent crashes
            PlayerSettings.gcIncremental = false;
            
            // Set conservative memory settings
            PlayerSettings.stripEngineCode = false;
            
            // Ensure proper scripting backend
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.Mono2x);
            
            // Set API compatibility level
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Standalone, ApiCompatibilityLevel.NET_Standard_2_0);
            
            Debug.Log("[MacOSBuildFix] macOS build settings applied successfully");
        }
    }

    public void OnPostprocessBuild(BuildReport report)
    {
        if (report.summary.platform == BuildTarget.StandaloneOSX)
        {
            Debug.Log("[MacOSBuildFix] Post-processing macOS build...");
            
            // Re-enable incremental GC after build
            PlayerSettings.gcIncremental = true;
            
            Debug.Log("[MacOSBuildFix] macOS post-processing completed");
        }
    }
}

/// <summary>
/// Editor utility to manually apply macOS fixes
/// </summary>
public class MacOSBuildUtility
{
    [MenuItem("Tools/macOS Build Fix/Apply Settings")]
    public static void ApplyMacOSSettings()
    {
        // Disable incremental GC
        PlayerSettings.gcIncremental = false;
        
        // Set conservative memory and threading settings
        PlayerSettings.stripEngineCode = false;
        PlayerSettings.runInBackground = false;
        
        // Use Mono instead of IL2CPP for better stability
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.Mono2x);
        
        // Set API compatibility
        PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Standalone, ApiCompatibilityLevel.NET_Standard_2_0);
        
        Debug.Log("macOS build settings applied. Incremental GC disabled, Mono backend selected.");
        
        EditorUtility.DisplayDialog("macOS Build Fix", 
            "Settings applied:\n" +
            "- Incremental GC: Disabled\n" +
            "- Scripting Backend: Mono\n" +
            "- Engine Code Stripping: Disabled\n" +
            "- Run in Background: Disabled", "OK");
    }
    
    [MenuItem("Tools/macOS Build Fix/Restore Default Settings")]
    public static void RestoreDefaultSettings()
    {
        // Restore default settings
        PlayerSettings.gcIncremental = true;
        PlayerSettings.stripEngineCode = true;
        PlayerSettings.runInBackground = true;
        
        Debug.Log("Default settings restored.");
        
        EditorUtility.DisplayDialog("macOS Build Fix", "Default settings restored.", "OK");
    }
}
