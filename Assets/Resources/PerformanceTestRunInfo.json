{"TestSuite": "", "Date": 0, "Player": {"Development": false, "ScreenWidth": 0, "ScreenHeight": 0, "ScreenRefreshRate": 0, "Fullscreen": false, "Vsync": 0, "AntiAliasing": 0, "Batchmode": false, "RenderThreadingMode": "MultiThreaded", "GpuSkinning": true, "Platform": "", "ColorSpace": "", "AnisotropicFiltering": "", "BlendWeights": "", "GraphicsApi": "", "ScriptingBackend": "Mono2x", "AndroidTargetSdkVersion": "AndroidApiLevelAuto", "AndroidBuildSystem": "<PERSON><PERSON><PERSON>", "BuildTarget": "StandaloneOSX", "StereoRenderingPath": "MultiPass"}, "Hardware": {"OperatingSystem": "", "DeviceModel": "", "DeviceName": "", "ProcessorType": "", "ProcessorCount": 0, "GraphicsDeviceName": "", "SystemMemorySizeMB": 0}, "Editor": {"Version": "6000.0.25f1", "Branch": "6000.0/release", "Changeset": "4859ab7b5a49", "Date": 1730184323}, "Dependencies": ["com.avaturn.webview@2.0.0", "com.nethereum.unity@4.27.0", "com.unity.2d.animation@10.1.4", "com.unity.2d.aseprite@1.1.9", "com.unity.2d.psdimporter@9.0.3", "com.unity.2d.sprite@1.0.0", "com.unity.2d.spriteshape@10.0.7", "com.unity.2d.tilemap@1.0.0", "com.unity.ai.navigation@2.0.8", "com.unity.animation.rigging@1.3.0", "com.unity.collab-proxy@2.5.2", "com.unity.feature.2d@2.0.1", "com.unity.formats.fbx@5.1.3", "com.unity.ide.rider@3.0.34", "com.unity.ide.visualstudio@2.0.22", "com.unity.ide.vscode@1.2.5", "com.unity.inputsystem@1.11.2", "com.unity.mobile.android-logcat@1.4.4", "com.unity.multiplayer.center@1.0.0", "com.unity.nuget.newtonsoft-json@3.2.1", "com.unity.recorder@5.1.2", "com.unity.render-pipelines.universal@17.0.3", "com.unity.render-pipelines.universal-config@17.0.3", "com.unity.shadergraph@17.0.3", "com.unity.test-framework@1.4.5", "com.unity.timeline@1.8.7", "com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4", "com.unity.ugui@2.0.0", "com.unity.visualeffectgraph@17.0.3", "com.unity.visualscripting@1.9.5", "com.unity.xr.hands@1.5.0", "com.unity.xr.interaction.toolkit@3.0.7", "com.unity.xr.management@4.5.0", "com.unity.xr.openxr@1.13.2", "com.unity.modules.accessibility@1.0.0", "com.unity.modules.ai@1.0.0", "com.unity.modules.androidjni@1.0.0", "com.unity.modules.animation@1.0.0", "com.unity.modules.assetbundle@1.0.0", "com.unity.modules.audio@1.0.0", "com.unity.modules.cloth@1.0.0", "com.unity.modules.director@1.0.0", "com.unity.modules.imageconversion@1.0.0", "com.unity.modules.imgui@1.0.0", "com.unity.modules.jsonserialize@1.0.0", "com.unity.modules.particlesystem@1.0.0", "com.unity.modules.physics@1.0.0", "com.unity.modules.physics2d@1.0.0", "com.unity.modules.screencapture@1.0.0", "com.unity.modules.terrain@1.0.0", "com.unity.modules.terrainphysics@1.0.0", "com.unity.modules.tilemap@1.0.0", "com.unity.modules.ui@1.0.0", "com.unity.modules.uielements@1.0.0", "com.unity.modules.umbra@1.0.0", "com.unity.modules.unityanalytics@1.0.0", "com.unity.modules.unitywebrequest@1.0.0", "com.unity.modules.unitywebrequestassetbundle@1.0.0", "com.unity.modules.unitywebrequestaudio@1.0.0", "com.unity.modules.unitywebrequesttexture@1.0.0", "com.unity.modules.unitywebrequestwww@1.0.0", "com.unity.modules.vehicles@1.0.0", "com.unity.modules.video@1.0.0", "com.unity.modules.vr@1.0.0", "com.unity.modules.wind@1.0.0", "com.unity.modules.xr@1.0.0", "com.unity.modules.subsystems@1.0.0", "com.unity.modules.hierarchycore@1.0.0", "com.unity.xr.core-utils@2.3.0", "com.unity.xr.legacyinputhelpers@2.1.11", "com.unity.mathematics@1.3.2", "com.unity.render-pipelines.core@17.0.3", "com.unity.sysroot@2.0.10", "com.unity.sysroot.linux-x86_64@2.0.9", "com.unity.ext.nunit@2.0.5", "com.unity.searcher@4.9.2", "com.unity.collections@2.5.1", "com.unity.bindings.openimageio@1.0.0", "com.autodesk.fbx@5.1.1", "com.unity.2d.pixel-perfect@5.0.3", "com.unity.2d.tilemap.extras@4.0.2", "com.unity.burst@1.8.18", "com.unity.2d.common@9.0.7", "com.unity.cloud.gltfast@5.2.0", "com.unity.cinemachine@2.10.1", "com.unity.rendering.light-transport@1.0.1", "com.unity.nuget.mono-cecil@1.11.4", "com.unity.test-framework.performance@3.0.3"], "Results": []}