﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>BestHTTP</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_6000_0_25;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_OSX;PLATFORM_STANDALONE;UNITY_STANDALONE_OSX;UNITY_STANDALONE;ENABLE_GAMECENTER;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;ENABLE_SPATIALTRACKING;PLATFORM_HAS_CUSTOM_MUTEX;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;VUPLEX_CCU;AVATURN;DOTWEEN;FISHNET;FISHNET_V4;EDGEGAP_PLUGIN_SERVERS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneOSX:2</UnityBuildTarget>
    <UnityVersion>6000.0.25f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/MiscPemGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/IdentifierType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/DataLengthException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9ECParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP128R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Strings.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/ValidityPreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HeartbeatMessageType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerStringBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SessionParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLTaggedObjectParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/KCcmBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsECDomain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/BasicOCSPResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509Utilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KeyTransRecipientInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCertificateRole.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertReqMessages.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/teletrust/TeleTrusTObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CrlException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERSetParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/OfferedPsks.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R2Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Implementations/WebGLBrowser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsStreamSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/FastTlsCrypto.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/IsoTrailers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/SignerIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/VerisignCzagExtension.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AesFastEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ConnectionEnd.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/InvalidCipherTextException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/ecc/MQVuserKeyingMaterial.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/IHTTPRequestHandler.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509V2AttributeCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/SubjectPublicKeyInfoFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServerContext.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509V1CertificateGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/AuthEnvelopedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCryptoUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/microsoft/MicrosoftObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSReadable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixPolicyNode.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedAsymmetricBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/SM2KeyExchangePublicParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/PeekableIncomingSegmentStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OriginatorIdentifierOrKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsEccUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/ProtocolEvents.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/CompressedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509ObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/RevocationKeyTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertAnnContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/OpenSSLPBEParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsSrp6Server.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsPeer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/DHPublicKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/HC128Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastTlsBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/ZlibConstants.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Messages/NegotiationResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/Attribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/MacSink.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HeaderTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/SocketOptions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/DigestSink.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UserAttributeSubpacketsReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/GenericSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509CertPairParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAttributeTableGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1TaggedObjectParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/WNafPreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/PKCS5Scheme2PBEKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPath.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTPProtocolFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathValidatorResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha224Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/GlvTypeBParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131R2Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/HelperClasses.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509ExtensionsGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/AsymmetricKeyParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/ProofOfPossessionSigningKeyBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerApplicationSpecific.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Websocket/WebSocketSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/ReadOnlyBufferedStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsClientProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CrlListID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CipherSuite.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CrlAnnContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/RespID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/gm/GMObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Frames/WebSocketFrameReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedIesCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AttributeCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/NameType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathValidator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/ServerSentEvents/Message.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Authentication/Credentials.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/OCSPObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/DHAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP1Handler.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PbmParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPUpdateDelegator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/SignerInformationStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/IndefiniteLengthInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMECapability.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/IL2CPP/Il2CppSetOptionAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIStatusInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemObjectGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/CipherUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampResponseGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/KeyParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSProcessableFile.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/srp/SRP6Server.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMECapabilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IXof.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/GOFBBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Shorts.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R2Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Implementations/WebSocketBaseImplementation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsSrp6VerifierGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsServerProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ProtocolName.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RevRepContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/EndoUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedDataGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHPrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs12Store.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Cast5Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERSetParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/TextureDownloadSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/TimeStampTokenEvidence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Transports/TransportBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ElGamalPublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/AttributeCertificateIssuer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/FixedPointUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/FileOutput.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/Extensions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/ServiceLocator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/Components/Cookies.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthenticatedGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECDomainParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/Pkcs7Padding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHValidationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ScaleXNegateYPointMap.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerVisibleString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/FixedPointCombMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IBlockResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/Autodetect/ProxyDetector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/abc/SimpleBigDecimal.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Haraka512_X86.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsReplayWindow.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509CertStoreSelector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/TimeStampedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SymmetricEncIntegrityPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/SemanticsInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/gm/SM2P256V1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SM2Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/S2k.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsHmac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/drbg/HMacSP800Drbg.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/PkcsException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/HostConnection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/DSTU7564Mac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SerpentEngineBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509Attribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsKeyExchangeFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/EdDsaPublicBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/Base64.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TSPAlgorithms.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/IBlockCipherMode.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x500/style/IetfUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Haraka256_X86.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RC2Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R2FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/SM3Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Ed448KeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPUtil.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SignatureScheme.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IEntropySource.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/TcpClient/WinRT/DataReaderWriterStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/CryptoApiEntropySourceProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/NaccacheSternEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc8032/Ed25519.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/RespData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/InvalidParameterException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc7748/X25519.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/DeclarationOfMajority.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/Components/Cache.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RFC3211WrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/Attributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/SM2KeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ScaleXPointMap.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO/SocketIOChatSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsECDheKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CertificatePair.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SecretKeyPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/StaticTree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/BcpgObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/QCStatement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/RecordPreview.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/IPreCompCallback.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/bc/BCObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/PkiArchiveControlBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsMacSink.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/RSAPrivateKeyStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1ParsingException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/BasicTlsSrpIdentity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSProcessable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyASN1InputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IBufferedCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastChaCha7539Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Kdf1BytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RijndaelEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x500/DirectoryString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/PreferredAlgorithms.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/IRandomGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/ECDHCBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113R2Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/ZeroBytePadding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedDataGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509KeyUsage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Caching/HTTPCacheFileLock.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CRLReason.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/KDFDoublePipelineIterationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192K1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat192.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/IControl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/KDFFeedbackBytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/IAsn1Encoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/KeyFlags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/FileSystem/IIOService.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/Tables64kGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Frames/WebSocketFrameTypes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/Revocable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HandshakeType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/AbstractTls13Client.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/gnu/GNUObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/GOST3410KeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ISignatureFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ISignerWithRecovery.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/SingleResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/CertStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs12Utilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/extension/X509ExtensionUtil.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/KeyException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/GCMBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CertificateNotYetValidException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Implementations/Utils/LockedBufferSegmenStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RevRepContentBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CompleteCertificateRefs.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/PaddedBufferedBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/AbstractECMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/HexEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/GenMsgContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Messages/ServerMessages.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/CSHAKEDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IMacFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/nist/NISTObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/ResponseData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SignaturePacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsImplUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Pkcs5S1ParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherSigningCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RSABlindingParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224K1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9ECParametersHolder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Caching/HTTPCacheService.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ByteQueue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Forms/HTTPFieldData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/SampleBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/Asn1DigestFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BEROctetStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/SignerId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/CrlID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/Srp6StandardGroups.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/Tables8kGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Ed448phSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Encodable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/ParserToken.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsSecret.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/NetscapeCertType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/Ssl3Utilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/PrincipalUtil.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ParametersWithSalt.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/DsaSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/JsonEncoders/IJSonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2WebSocketStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha256Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IVerifierFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/Asn1KeyWrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Messages/IServerMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/NullDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/HubWithPreAuthorizationSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AesWrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Dstu7624Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PBES2Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1TaggedObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/OCSPResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SecurityParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/LegacyTls13Verifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/ETSIQCObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Tag.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Transports/WebSocketTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/BasicTlsPskExternal.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMECapabilitiesAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertRepMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UserIdPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CrlValidatedID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/DefaultTls13Client.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP128R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/ContentInfoParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/ISO7816d4Padding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/tsp/Accuracy.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IEncapsulatedSecretGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/rosstandart/RosstandartObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/PskTlsClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/compression/ZLib.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/EmbeddedSignature.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/DHStandardGroups.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/GF2Polynomial.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ModDetectionCodePacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsCloseable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X25519KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Collections/Concurrent/ConcurrentQueue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/GeneralSubtree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/GOST3411_2012Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherHashAlgAndValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/RevokedInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/Evidence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/FileSystem/DefaultIOService.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsSrpConfigVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ElGamalPublicBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCrypto.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/NonMemoableDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/RecipientInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/GeneralNames.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerOctetString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DHParametersHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/HTTPRequestAsyncExtensions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/WNafL2RMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Caching/HTTPCacheMaintananceParams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SignatureAndHashAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/SignerUserId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcX448.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/IdeaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/CertificateStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/MemoableResetException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonWriter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X25519PrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Authentication/IAuthenticationProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/PEMUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163R2Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BEROctetStringGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/HandshakeData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/WNafUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RSABlindedEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerNull.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R2Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Text/StringBuilderPool.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Interfaces.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/ECDsaSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSrp6Server.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/GenRepContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastSalsa20EngineHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/sigi/PersonalData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/RevokedStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/SM2Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1RelativeOid.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthenticatedDataGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsRecordLayer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/attr/ImageAttrib.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/RandomDsaKCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsHeartbeat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/TrustPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/CmpException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/Srp6Group.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/KDFDoublePipelineIterationBytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/Crc24.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131R2Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x500/Rdn.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/RoleSyntax.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/UnityOutput.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509Crl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/HarakaBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastCcmBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPskKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Forms/Implementations/HTTPUrlEncodedForm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO/SocketIO Json Encoders/JsonDotNetEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Transports/WebSocketTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/BufferedDecoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1SequenceParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/GlvTypeAParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherCertID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/Tables4kGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Blake2sDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsNoCloseNotifyException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Tags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/IEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/SecureRandom.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDssSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OcspIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163R2Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerBitString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/SCVPReqRes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Implementations/OverHTTP2.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Transports/PollingTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/gm/GMNamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/PssSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/date/DateTimeUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed25519PublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsStreamVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP128R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Blake3Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2FrameHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SrpTlsServer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/StoreImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPRespStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/IDigestCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/AbstractECLookupTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IAsymmetricBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedDataStreamGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs12Entry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DEROctetStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/TrustSignature.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDSA.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TSPValidationException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/PKMacBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/nsri/NsriObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERSetGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/gm/SM2P256V1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRsaVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/GeneralName.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/CbcBlockCipherMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/JsonProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/Controls.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/CMSAttributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/DsaPublicBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/XofUtils.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsHmac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Transports/ITransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/abc/ZTauElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RsaPrivateCrtKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsECDomain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSCompressedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Cast6Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLBitString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1Generator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AuthorityKeyIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PopoSigningKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SignatureSubpacketsReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/CertificationRequestInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertReqMsg.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsEncryptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509V2AttributeCertificateGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsNonceGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/PrivateKeyUsagePeriod.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/CircularBuffer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Forms/HTTPFormUsage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsCredentialedSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonMapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/kdf/DHKekGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/IEncryptedValuePadder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AttributeCertificateInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERSequenceGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDHDomain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECPublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/BaseOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ICipherParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Dstu7624WrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ParametersWithID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/DefaultVerifierCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/SelectorUI/Category.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/nist/KMACwithSHAKE256_params.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixAttrCertPathValidator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastTlsAeadCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/CryptoSignatureAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Mgf1BytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/KeyValuePairList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/OcspStatusRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyDLEnumerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/MD2Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/CbcBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSecret.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Utilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/Lexer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerApplicationSpecificParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Enums.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Transports/WebSocketTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DeferredHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509Certificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Ed25519ctxSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathValidatorException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPeer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UserAttributePacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/Tls13Verifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/Packet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/IssuerAndSerialNumber.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerBoolean.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/Restriction.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ElGamalKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathChecker.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsDHDomain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/PrimaryUserId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedAeadCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSCompressedDataGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSContentInfoParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/Security.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/KdfParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509CrlParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CRLNumber.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/X931Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PKMacValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/CMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DatagramTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/BasicOCSPRespGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/TeeInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DesEdeParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/KEKRecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP384R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/eac/EACObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDerivationFunction.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ntt/NTTObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampTokenInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsDHUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakePrimeOrderGroup.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/ECGOST3410NamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/GOST28147Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRsaPssVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/EncryptionScheme.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/EcbBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/BasicGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathBuilderException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixBuilderParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Sequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/Signature.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ServerHello.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPsk.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Events/EventDescriptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/Time.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/BasicConstraints.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OcspListID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Events/EventTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemWriter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PopoDecKeyRespContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/EncryptedValueBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ElGamalSecretBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DsaKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/MacStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/NaccacheSternPrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/fpe/FpeEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/PskTlsServer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP521R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SrpTlsClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Caching/HTTPCacheFileInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/ChaCha7539Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/DigestUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/HubConnection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/IExtensionField.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/SignaturePolicyIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/SHA3Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/SkeinParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/ReadOnlyDictionary.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ISecretWithEncapsulation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/HexTranslator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HandshakeMessageInput.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/srp/SRP6Utilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalR/Json Encoders/LitJsonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerGraphicString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/ECEndomorphism.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Server-Sent Events/SimpleSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/InfBlocks.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcVerifyingStreamSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsKeyExchangeFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/PushbackStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Null.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/ResponderID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Error.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/TrustAnchor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/KMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/sec/ECPrivateKeyStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/SignatureException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/SignerAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/KDFCounterBytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/Deflate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZDeflaterOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/DHBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TSPException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DesEdeKeyGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/MarkerPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RsaKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/GOST3410DigestSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/TEAEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Authentication/Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/Rfc3280CertPathUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/encodings/OaepEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OriginatorInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsSuiteHmac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1OutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509DefaultEntryConverter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/BufferSegmentStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/UrlAndHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/KeccakDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X962Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/util/AlgorithmIdentifierFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/StreamOverflowException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcSsl3Hmac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ScaleYNegateXPointMap.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/RecipientInformationStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/ESSCertID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/mozilla/PublicKeyAndChallenge.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/SigPolicyQualifierInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakeRound1Payload.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/IX509Extension.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/HeartbeatManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/anssi/ANSSINamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ECPointFormat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/IesEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastSalsa20Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/SocketManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/RecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsClientContextImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/IJsonWrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/OtherName.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Ed448Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/JsonEncoders/DefaultJSonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/IBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/ISO9797Alg3Mac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO3/ChatSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/EncKeyWithID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/GZipDecompressor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/TigerDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/GZipStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/ProcurationSyntax.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/Streams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Memory/BufferStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/DefaultSignatureCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/BCrypt.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat576.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/Deflate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsServer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Messages/Invocation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/MD4Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/MaxFragmentLength.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/UploadItemController.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IVerifierFactoryProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/PublicKeyFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/HostManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SignatureSubpacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HeartbeatExtension.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyDLSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/Holder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/RevocationValues.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DefiniteLengthInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/OutputStreamPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/ElGamalParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsNullNullCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RootCaKeyUpdateContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/GlvMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPReq.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsContext.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/ElGamalKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/SignerInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/KeyDerivationFunc.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DesParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/IStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256K1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/ISO10126d2Padding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/GMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/ECDHWithKdfBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/RecordFormat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherRevVals.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Set.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/PrfAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/IDsaKCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/SkeinMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat224.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSrpConfig.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/NamedGroup.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPskIdentity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ProtocolVersion.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CommitmentTypeQualifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPResp.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CertificateExpiredException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerBitStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PKCS12PBEParams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/util/CipherFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/Base64Encoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/icao/LDSVersionInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/Features.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc7748/X25519Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptlib/CryptlibObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastChaCha7539EngineHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/Asn1CipherBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/ProtectedPkiMessageBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsAeadCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/AuthEnvelopedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/AbstractTlsCrypto.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/NoekeonEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/CrmfException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/ContentHints.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CAKeyUpdAnnContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/PacketTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/GlvTypeBEndomorphism.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DHKeyGeneratorHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AlertLevel.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemGenerationException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Transports/WebsocketTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPskIdentityManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/Autodetect/AndroidProxyDetector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/CollectionUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DsaParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/fpe/FpeFf3_1Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192K1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/Challenge.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1UtcTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/RecipientId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PKIPublicationInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ServerOnlyTlsAuthentication.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/IPolynomialExtensionField.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SignatureSubpacketTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/BlockCipherPadding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/TweakableBlockCipherParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Longs.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/Check.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIFreeText.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsFatalAlertReceived.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/CertStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/ECNRSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/IL2CPP/Il2CppEagerStaticClassConstructionAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/RequestEvents.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonMockWrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsDHKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSession.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AesEngine_X86.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RSACoreEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/BcpgInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastChaChaEngineHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT239K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/fpe/FpeFf1Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IWrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/CertificateConfirmationContentBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CertificateValues.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalR/Authentication Providers/SampleHeaderAuthentication.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/ECMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/Inflate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/IAeadCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Spans.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/ProtectedPkiMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Blake2xsDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/DefaultSignedAttributeTableGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AuthorityInformationAccess.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/ChaChaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2Frames.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AesLightEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509AttrCertStoreSelector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/PEMParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CertificateList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/PasswordRecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakeRound2Payload.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Mod.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/oiw/OIWObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Connection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IAsymmetricCipherKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/CryptoException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/OCSPResponseStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Authentication Providers/HeaderAuthenticator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/UserMappingType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/BaseKdfBytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/ProfessionInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/SignerStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrtpUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIHeader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/TBSRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/JksStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/encodings/ISO9796d1Encoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CompressionMethod.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ElGamalPrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/FpeParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastAesEngineHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsStreamSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertChainType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/RecordStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ByteQueueInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP521R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CrlSource.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/WrapperUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/ReasonFlags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SupplementalDataType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP384R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/ShakeDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/DesEdeWrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMEAttributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/sec/SECObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSCompressedDataStreamGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/Pkcs8Generator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/GlvEndomorphism.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ClientCertificateType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcX25519.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UserAttributeSubpacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/WebSocketStatusCodes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/gm/SM2P256V1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsDHConfig.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509NameEntryConverter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSrp6VerifierGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/ReadOnlyList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/PublicKeyEncSessionPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IKeyUnwrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AttCertValidityPeriod.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/Timer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/KeyExpirationTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/WhirlpoolDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/SCrypt.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/SimpleAttributeTableGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ec/CustomNamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsEd25519Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsHandshakeRetransmit.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/V2TBSCertListGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IAlphabetMapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/ParallelHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsFatalAlert.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertOrEncCert.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/EncryptedPrivateKeyInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/PublicKeyAlgorithmTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Transports/PollingTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServerContextImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/ISelector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/JsonEncoders/IJsonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/ProofOfPossession.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthenticatedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/AttributeCertificateHolder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECCurve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsBlockCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/SecurityUtilityException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/ZTree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509Extension.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/FixedPointPreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsServer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakeParticipant.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1UniversalType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/CAST5CBCParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/WTauNafMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Encoders/LitJsonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ISigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SimulatedTlsSrpIdentityManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Salsa20Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerNumericString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/IProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Objects.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/EncryptionAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/PrivateKeyInfoFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/AuthenticatorControl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat448.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat384.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPRespGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Forms/Implementations/HTTPMultiPartForm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs10CertificationRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/ECGOST3410Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224K1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ConstructedLazyDLEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/kdf/ConcatenationKdfGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509CollectionStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1SetParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDigestFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RFC3394WrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OtherRevRefs.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/RecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixAttrCertPathBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastTlsBlockCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SM4Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsECDsa13Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Parsers/DefaultJsonParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/OobCert.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDssVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT239Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/X931Rng.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP521R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509V2CRLGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Blake2bDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/util/FilterStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Person.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/WWWAuthenticateHeaderParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CipherType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerGeneralizedTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/OptionalValidity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerT61String.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/net/IPAddress.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Cookies/Cookie.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X448PrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSStreamException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509CertificateParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/MPInteger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ParametersWithRandom.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/DistributionPoint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/kisa/KISAObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Arrays.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/ECGOST3410ParamSetParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerUTCTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha384Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/ECDHBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PollRepContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/SipHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Enums.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/icao/LDSSecurityObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/CamelliaWrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertTemplate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECAlgorithms.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Ed25519phSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/CryptoApiRandomGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsECDHKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO/SocketIO Json Encoders/LitJsonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO3/Parsers/JsonDotNetParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/NestedMessageContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/OCSPRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/CMSObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KEKRecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AesEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/NoticeReference.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/SubjectDirectoryAttributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Extensions/PerMessageCompression.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/BaseDigestCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/IDrbgProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/DesEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/RevocationReasonTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509CertificateStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509ExtensionBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/HubWithAuthorizationSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/GeneralSecurityException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaValidationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/OnePassSignaturePacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/SignedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/GOST3411_2012_256Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/FileSystem/NETFXCOREIOService.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SupplementalDataEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/PlainDsaEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AbstractTlsContext.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/CmsContentEncryptorBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/ExtendedKeyUsage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Bytes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/Primes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/GUIHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/ECMqvWithKdfBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/ocsp/RequestedCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsHeartbeat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AttributeTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsAeadCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PollReqContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509CollectionStoreParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastPoly1305.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/IMemoable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/OcspResponsesID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IMacDerivationFunction.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/fpe/SP80038G.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Error.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/RevocationReason.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/IAsn1Choice.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastBcChaCha20Poly1305.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HandshakeMessageOutput.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/GcmSivBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/LinkedDictionary.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Events/TypedEventTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Cookies/CookieJar.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/CipherStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/ResumableStreamingSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/CryptoProObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerEnumerated.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/SelectorUI/ExampleListItem.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ConstructedOctetStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/Tables1kGcmExponentiator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/CompressedDataPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/OriginatorId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsECDsaSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CrlIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/IncomingPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/GcmUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSPBEKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/UrlBase64Encoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/BurstTables8kGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/CertBag.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/ErrorMsgContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/UserNotice.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/ContentIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLTaggedObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/DistributionPointName.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRsaSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/SelectorUI/ExampleInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/PbeParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/MD5Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Messages/Message.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Timings/TimingEventNames.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OtherRecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/iana/IANAObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/EncryptedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/GOST3410ParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/ESFAttributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/AssetBundleSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/RecipientIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ua/UAObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/TbcPadding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthenticatedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/PublicSubkeyPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ContentType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/icao/DataGroupHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerInteger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/DefaultPKMacPrimitivesProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Pkcs12ParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcChaCha20Poly1305.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/IEncodable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakePrimeOrderGroups.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CertificatePolicies.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/PolicyQualifierId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/IPolynomial.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ArmoredOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/MacUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/ProtectedPart.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMECapabilityVector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/Request.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixAttrCertChecker.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/ILogger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/HeaderValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/KeyLogFileWriter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KEKRecipientInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/SinglePubInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/EntropyUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ContainedPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/IDsaEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/PluginEvents.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/LiteralDataPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DHKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/NamingAuthority.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/DefaultLogger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathValidatorUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/sigi/SigIObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/AuthenticatedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Transports/TransportBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CommitmentTypeIndication.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/OtherInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Interfaces.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2Response.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Transports/ITransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpIdentityManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Forms/HTTPFormBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509CertificatePair.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1BitStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/DisplayText.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/IPKMacPrimitivesProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9ObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/MqvPublicParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC4Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/Time.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/KeyAgreeRecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X448PublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/RC2CBCParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/DigestInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/RipeMD256Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/GOST28147Mac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KeyAgreeRecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/BasicGcmExponentiator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs8EncryptedPrivateKeyInfoBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerTaggedObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Ed25519KeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RevAnnContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastGcmBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/Admissions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixNameConstraintValidatorException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcX25519Domain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ParametersWithSBox.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/OpenPgpCfbBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/Poly1305.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed448PublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/CertificateID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/KCtrBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsDHGroupVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/KeyPurposeId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ICipherBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Hubs/IHub.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/DesEdeEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/BasicEntropySourceProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateCompressionAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/CRC32.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/BigInteger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/AsyncTestHubSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/TimeStampAndCRL.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ChangeCipherSpec.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/BinaryReaders.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/Iso4217CurrencyCode.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/PkcsIOException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECFieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PBEParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Threading/ThreadedRunner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/GeneralDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/teletrust/TeleTrusTNamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RSABlindingEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/ISISMTTObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/OriginatorInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIConfirmContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Bits.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/EncryptedContentInfoParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemHeader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410KeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Transports/PollingTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/RevocationKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/util/BasicAlphabetMapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1ObjectDescriptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PrivateKeyInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/TeeOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/TupleHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Interleave.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TrustedAuthority.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/BigIntegers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/tsp/MessageImprint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/AdditionalInformationSyntax.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/BinaryWriters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/NamedGroupRole.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/SOCKSProxy.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/Exportable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/KEKIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECLookupTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/CtsBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/extension/SubjectKeyIdentifierStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/RsaPublicBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/OCSPReqGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/compression/Zip.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerVideotexString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RevDetails.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsDecodeResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECGOST3410Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/RFC3739QCObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateStatusType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/ObjectDigestInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Encoders/MessagePackProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT239FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/IAsn1Convertible.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/NameConstraints.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/KeyTransRecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECPoint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRsaPssSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Events/EventNames.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/TwofishEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ECPointMap.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193R2Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Socket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ICipherBuilderWithKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ElGamalParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/GOST3411_2012_512Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509StoreFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/ocsp/CertHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ClientAuthenticationType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2PluginSettings.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSessionImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/WebGLConnection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/SignerInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ConstructedILEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/Translator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IEncapsulatedSecretExtractor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampToken.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Encoders/MessagePackCSharpProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OtherRevocationInfoFormat.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/HKDFParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/OCBBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Kdf2BytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc7748/X448.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/sec/SECNamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CombinedHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha512tDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/SingleResp.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECPrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/util/CipherKeyGeneratorFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRawKeyCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/PrimeField.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Events/EventNames.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Iso9796d2Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/IesParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/RipeMD128Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed448PrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/EnvelopedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC532Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/PasswordRecipientInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/IAeadBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/IGcmExponentiator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/CertificateConfirmationContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/FiniteFields.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/TestHubSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECNamedDomainParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastGcmBlockCipherHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/KDFFeedbackParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CRLDistPoint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Enums.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/X448Agreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/srp/SRP6VerifierGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs10CertificationRequestDelaySigned.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/DigestRandomGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/MgfParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/ReadOnlySet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/AuthenticatedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/ThreadedLogger.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/MonetaryLimit.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedStreamCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/Pfx.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HPACKEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/gm/SM2P256V1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/HKDFBytesGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UserAttributeSubpacketTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/RevocationDetailsBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalR/Authentication Providers/SampleCookieAuthentication.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat128.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/drbg/DrbgUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/SignatureCreationTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/SignerLocation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerUTF8String.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/NullEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2Stream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/HashAlgorithmTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Extensions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SEEDWrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/SicBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Frames/WebSocketFrame.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/ElGamalEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/BufferPoolMemoryStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerUniversalString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/JSON.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSUtils.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/UrlBase64.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/RedirectSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/Grain128AEADEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/Autodetect/FrameworkProxyDetector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/EncryptedPrivateKeyInfoFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HashAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/StreamBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/MultipartFormDataStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/HeaderParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/SimpleBlockResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/EncryptedValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/icao/ICAOObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/RipeMD160Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerBMPString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/anssi/ANSSIObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ServerName.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/SkeinEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDsaSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPMethods.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/SocketOptions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/BufferedReadNetworkStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/ConnectionHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/KeyUsage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/PolicyQualifierInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampRequestGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsReassembler.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsDHanonKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/PskIdentity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCertPathBuilderResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410ValidationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsReliableHandshake.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha512Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/DhbmParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedAeadBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSCompressedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/PKCS12StoreBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SkipjackEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/OidTokenizer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsRsaEncryptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/ReasonsMask.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDsaVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/NewSessionTicket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/BasicOCSPResp.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/Link.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Collections/ObjectModel/ObservableDictionary.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410PublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/OpenBsdBCrypt.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/srp/SRP6StandardGroups.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X962NamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/V1TBSCertificateGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Haraka512Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC564Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Platform.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsClientContext.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IRsa.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ServerNameList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemObjectParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyDERSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/V2Form.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/IAuthenticationProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/SigningCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/djb/Curve25519Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastTlsAeadCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ElGamalKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/KeyUpdateRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/InvalidKeyException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/CamelliaLightEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X448KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/IssuerAndSerialNumber.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/ZlibCodec.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/UploadHubSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SymmetricKeyEncSessionPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/CamelliaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/SignerSink.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/GOST3410Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/DefaultSignatureResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Iso9796d2PssSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/CertID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/OriginatorInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/IesWithCipherParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256K1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/RecipientKeyIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/ConnectionBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SymmetricEncDataPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertifiedKeyPair.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/util/Asn1Dump.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KeyTransRecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/KeyAgreeRecipientIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/NegotiationData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/ParameterUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SignatureAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat512.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/ReadOnlyCollection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113R2Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/PasswordRecipientInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ISO18033KDFParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CmpObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/ChaCha20Poly1305.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsHashSink.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/CertificateRequestMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/AbstractTlsSecret.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaPublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Blake3Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/bzip2/CBZip2InputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcDefaultTlsCredentialedSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Poly1305KeyGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/IFiniteField.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerApplicationSpecific.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HeartbeatMode.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDerivationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/PrivateKeyFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509Name.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/ICipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/ISAACEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DefaultTlsKeyExchangeFactory.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2Handler.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed25519PrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/StandardDsaEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/XTEAEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/NaccacheSternKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/sigi/NameOrPseudonym.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PKIArchiveOptions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Extensions/IExtension.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/Adler32.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPRange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/WebSocket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastChaChaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/SocketManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/DHStandardGroups.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSProcessableByteArray.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/GeneratorUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/DSTU7564Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9FieldID.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Timings/TimingCollector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/KeyShareEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/MonetaryValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/GOST3411Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/EncryptedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/tsp/TimeStampReq.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509NameTokenizer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HuffmanEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/DefaultVerifierResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CertificateEncodingException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/oiw/ElGamalParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Type.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/AsymmetricKeyEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ConstructedDLEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERSequenceParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SecretSubkeyPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/CompressionAlgorithmTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/ECNamedCurveTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/nist/NISTNamedCurves.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DigitallySigned.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/Zlib.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/Implementations/OverHTTP1.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTPConnection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ByteQueueOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/RsaSecretBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/Inflate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/MacData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/EndoPreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/ECMqvBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP384R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IKeyWrapper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RC5Parameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DSAParameterGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/drbg/CtrSP800Drbg.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Authentication/DefaultAccessTokenAuthenticator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509Extensions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpLoginParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/RevocationDetails.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/ZlibBaseStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/UseSrtpData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastAesEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerIA5String.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/PrimitiveEncoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakeRound3Payload.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PopoSigningKeyInput.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/X25519PublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IRawAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/X509Attributes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServerProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/CfbBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/drbg/HashSP800Drbg.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/PrimitiveEncodingSuffixed.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertTemplateBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsECDHanonKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/kdf/DHKdfParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/KeySpecificInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Authentication/DigestStore.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/LongArray.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/NaccacheSternKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/util/Pack.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/InputStreamPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/UnsupportedPacketVersionException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/VMPCEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/AttributeTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/IDecompressor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1UniversalTypes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyDERSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/SignerInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ECPublicBCPGKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/DHDomainParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIFailureInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/JSON/LitJson/JsonReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/TextListItem.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/SubsequentMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERExternalParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/TcpClient/TcpClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc8032/Ed448.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/Attribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/RegTokenControl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed448KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixNameConstraintValidator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerPrintableString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC2Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/LongDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/PKCS5Scheme2UTF8PBEKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSecureReadable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/SimpleLookupTable.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAttributeTableGenerationException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerObjectIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/KeyExchangeAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/OfbBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Socket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC6Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/SP800SecureRandom.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CompleteRevocationRefs.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1StreamParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/MiscObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/EncryptedKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/RsaKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerGeneralString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/GOST28147Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/CertificateStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/abc/Tnaf.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpConfigVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/CryptoHashAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT233FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSEnvelopedDataStreamGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DatagramSender.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DesKeyGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/IssuingDistributionPoint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/DefaultAuthenticatedAttributeTableGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsHandshakeHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/HandshakeData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Memory/BufferSegment.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsExtensionsUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/KeyAgreeRecipientInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsTimeoutException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AlgorithmIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/Certificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/smime/SMIMEEncryptionKeyPreferenceAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthenticatedDataStreamGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERBitString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/HMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/DeflateStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/ScalarSplitParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/PkixCrlUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateVerify.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LimitedInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/tsp/TSTInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/HashSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/PopoPrivKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Object.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/rfc7748/X448Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/SafeBag.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CachedInformationType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT283K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/TBSCertificateStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsCredentialedDecryptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ArmoredInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/GOST3410NamedParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/Ed25519Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsEd448Signer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsCredentialedSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/FilterStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AttCertIssuer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/tsp/TimeStampResp.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERSequenceGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/HTTP2SettingsRegistry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/HeartbeatMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/kdf/ECDHKekGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsNullCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsDheKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x500/AttributeTypeAndValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OriginatorPublicKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CrmfObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLBitStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/BiometricData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ECDsaPublicBCPGKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSProcessableInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsCredentialedAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/pem/PemReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PKCSObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/ConnectionEvents.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/TimeStampedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT409Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/MemoryInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/InfTree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/RecipientEncryptedKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/BaseInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9ECPoint.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1Exception.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Haraka256Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/MacAlgorithm.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/CmsKeyTransRecipientInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/SymmetricKeyAlgorithmTags.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIMessages.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/ShortenedDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509AttrCertParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaPrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/EncryptedContentInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/IssuerKeyId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/ThreefishEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/GOST3410ParamSetParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ServerSrpParams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/VMPCRandomGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/HTTPManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/FramesAsStreamView.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsDH.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsECDH.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CrlStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PopoDecKeyChallContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/EAXBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/srp/SRP6Client.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Timings/TimingEvent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/InfCodes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/RsaKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cmp/GeneralPkiMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Transports/ServerSentEventsTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/BlowfishEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/Autodetect/ProgrammaticallyAddedProxyDetector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/UploadStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/ServerSentEvents/EventSource.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/KDFCounterParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/OobCertHash.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DigestInputBuffer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/IDEACBCPar.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/djb/Curve25519.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO/Packet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/X509CertificateEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CounterSignatureDigestCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DHBasicKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat256.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ConstructedBitStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/PolicyInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsECDsaVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/DsaDigestSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/V2AttributeCertificateInfoGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/GOST3410PrivateKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/SrtpProtectionProfile.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/PEMWriter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509SignatureUtil.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthEnvelopedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Ed25519KeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/DHParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/AriaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalRCore/Encoders/JsonDotNetEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/SignerInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkcs/Pkcs8EncryptedPrivateKeyInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/TcpClient/WinRT/TcpClient.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/Req.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ASN1OctetStringParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/RSAESOAEPparams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/EnvelopedDataHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/Target.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastCbcBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Collections/Specialized/NotifyCollectionChangedEventArgs.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/gcm/IGcmMultiplier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/Asn1Signature.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Enums.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/bzip2/CRC.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT163K1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/RSASSAPSSparams.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DHPublicKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/WriteOnlyBufferedStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/IssuerSerial.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/TnepresEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509CertPairStoreSelector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/bzip2/CBZip2OutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ECDHPublicBCPGKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ParametersWithIV.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsBlockCipherImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/X931SecureRandom.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/AgreementUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ocsp/ResponseBytes.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/StreamList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/SigningCertificateV2.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERSetGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP384R1FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/Srp6GroupParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193R2Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTls13Verifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServerCertificateImpl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIHeaderBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSSignedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsEpoch.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/DsaSecretBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/RipeMD320Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/Sha1Digest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/CfbBlockCipherMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CertificateException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZInflaterInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/PasswordException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/AesUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/CertRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/DSTU7624Mac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERSequenceParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcX448Domain.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RC2WrapEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/misc/NetscapeRevocationURL.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/KeyRecRepContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1InputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/bsi/BsiObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/Attribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERTaggedObjectParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/macs/VMPCMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TSPUtil.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/SM2KeyExchangePrivateParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/FileConnection.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Logger/LoggingContext.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/X448KeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/bc/LinkedCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateUrl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IStreamCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/BufferedCipherBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509CrlEntry.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/pkix/Rfc3281CertPathUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/encodings/Pkcs1Encoding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/AsymmetricCipherKeyPair.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/LimitedInputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/SignerUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalRCore/Transports/LongPollingTransport.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP224R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSTypedStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DERExternal.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/TargetInformation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1GeneralizedTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/JsonEncoders/DefaultJsonEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/Targets.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/digests/SkeinDigest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ECCurveType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CommitmentTypeIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/GenTimeAccuracy.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/X931SecureRandomBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/MetaData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/PBKDF2Params.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/IetfAttrSyntax.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/PEMReader.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/WTauNafPreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/RsaUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DerTaggedObject.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/crmf/AttributeTypeAndValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT193R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTP2/BufferHelper.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/RevReqContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/EdSecretBcpgKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsRsaKeyExchange.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/X923Padding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/SignatureExpirationTime.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/RSABlindingFactorGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/PskKeyExchangeMode.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/sig/NotationData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/ReversedWindowGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertReqTemplateContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IDecryptorBuilderProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/SP800SecureRandomBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CmpCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/edec/EdECObjectIdentifiers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/BufferedEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsEncodeResult.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/EncryptionException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/IL2CPP/PreserveAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/OutputLengthException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SEEDEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/AEADParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/X25519KeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/BrotliDecompressor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat160.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ExporterLabel.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSAuthEnvelopedGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsAeadCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/multiplier/PreCompInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/cms/CMSException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/CipherKeyGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Transports/PostSendTransportBase.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/qualified/TypeOfBiometricData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/modes/CcmBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/jpake/JPakeUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/InfoTypeAndValue.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsCredentials.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/HMacDsaKCalculator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/AccessDescription.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Helpers/SelectorUI/SampleSelectorUI.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/DHValidationParms.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/nist/KMACwithSHAKE128_params.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/X509V3CertificateGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/AuthenticatedSafe.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateStatusRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ExperimentalPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcDefaultTlsCredentialedAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsDHGroupVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/MemoryOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/collections/EnumerableProxy.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/extension/AuthorityKeyIdentifierStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ChannelBinding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/icao/CscaMasterList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TLS/Crypto/Impl/FastSicBlockCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/SubjectKeyIdentifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/DHParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/HTTPConnectionStates.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/ScaleYPointMap.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/encoders/Hex.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/operators/GenericKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/Autodetect/EnvironmentProxyDetector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT239K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/RSAPublicKeyStructure.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/Timeout.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IEntropySourceProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/ContentInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BerOctetString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/HC256Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/CrlOcspRef.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/endo/GlvTypeAEndomorphism.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/VMPCKSA3Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/Pkcs5S2ParametersGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/XSalsa20Engine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/Integers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Memory/BufferPool.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsSrp6Client.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsECConfig.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/DHGroup.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/EnvelopedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/bzip2/BZip2Constants.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/DLSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/CertPolicyId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/PbeUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Decompression/InfTree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP521R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/AlertDescription.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Core/HostDefinition.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/signers/RsaDigestSigner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/io/DigestStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/ocsp/UnknownStatus.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IStreamCipher.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/SubjectPublicKeyInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/agreement/X25519Agreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/PkiArchiveControl.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Extensions/Future.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crmf/CertificateRequestMessageBuilder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/cert/CertificateParsingException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP192R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/JZlib.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT571K1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/CertificationRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Hubs/Hub.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCryptoException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/PEMException.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP128R1Field.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/PublicKeyPacket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/DSAParameter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/HTTP/StreamingSample.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/PlatformSupport/Threading/LockHelpers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsCrypto.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/DsaParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/io/compression/Bzip2.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP256R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/prng/drbg/ISP80090Drbg.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DtlsClientProtocol.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsSrpIdentity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SocketIO.3/Parsers/IParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/raw/Nat320.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsNonceGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsStreamVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/pkcs/SignedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsPskExternal.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/TlsSuiteMac.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/Plugin/AsyncExtensions.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/NaccacheSternKeyGenerationParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/ContentInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/TBSCertList.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/PKIBody.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SampleRoot.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IVerifier.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/ECKeyParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/SignedDataParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcTlsSrp6Client.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Proxies/HTTPProxy.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/DatagramReceiver.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/PolicyMappings.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cryptopro/GOST3410PublicKeyAlgParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/esf/SignaturePolicyId.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SignalR/Messages/ClientMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT131FieldElement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tsp/TimeStampTokenGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/generators/ECKeyPairGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ClientHello.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x9/X9IntegerConverter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecP160R1Point.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/ExtensionType.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1OctetString.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/OtherKeyAttribute.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/security/DotNetUtilities.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/BERGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/BasicTlsPskIdentity.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/CryptoServicesRegistrar.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/field/GenericPolynomialExtensionField.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SocketIO3/Parsers/MsgPackParser.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/ess/ESSCertIDv2.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/isismtt/x509/AdmissionSyntax.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/x509/store/X509CrlStoreSelector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/ECSecretBCPGKey.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/TlsCryptoParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cmp/CertConfirmContent.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/LazyDLSet.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/RsaEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsAuthentication.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/crypto/impl/bc/BcDefaultTlsCredentialedDecryptor.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/engines/SerpentEngine.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Examples/SignalR/Json Encoders/JSonDotnetEncoder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/CertificateStatusRequestItemV2.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/x509/V3TBSCertificateGenerator.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/util/zlib/ZTree.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/Asn1EncodableVector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/IAsn1String.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/openssl/IPasswordFinder.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/asn1/cms/CompressedData.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/parameters/MqvPrivateParameters.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/paddings/IBlockCipherPadding.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/WebSocket/WebSocketResponse.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/IBasicAgreement.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/bcpg/BcpgOutputStream.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/math/ec/custom/sec/SecT113R1Curve.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServer.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/tls/TlsServerCertificate.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/Connections/TCPConnector.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/crypto/MaxBytesExceededException.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/BestHTTP.asmdef" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Third-Party Notices.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/license.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/ReleaseNotes.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/link_android_subset.xml" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Source/SecureProtocol/License.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/link.xml" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/Best HTTP/Documentation.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.LinuxStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.RPC">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.RPC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Util">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Util.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.ABI">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.ABI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.RLP">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.RLP.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Mud.Contracts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Mud.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Geth">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Geth.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Optimism">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Optimism.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.BlockchainProcessing">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.BlockchainProcessing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Model">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FishNet.CodeAnalysis">
      <HintPath>Assets/FishNet/Runtime/Plugins/CodeAnalysis/FishNet.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>Assets/MetaMask/Plugins/Libraries/zxing.unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Signer">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Signer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ImpromptuInterface">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/ImpromptuInterface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="evm.net">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Runtime/netstandard2.1/evm.net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Siwe.Core">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Siwe.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.IOSResolver">
      <HintPath>Assets/ExternalDependencyManager/Editor/1.2.183/Google.IOSResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ADRaffy.ENSNormalize">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/ADRaffy.ENSNormalize.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Merkle.Patricia">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Merkle.Patricia.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EventEmitter.NET">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/EventEmitter.NET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.RpcClient">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.JsonRpc.RpcClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reactive">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Reactive.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>Assets/MetaMask/Plugins/Libraries/BouncyCastle.Crypto.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.HdWallet">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.HdWallet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.PackageManagerResolver">
      <HintPath>Assets/ExternalDependencyManager/Editor/1.2.183/Google.PackageManagerResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MetaMask.SDK">
      <HintPath>Assets/MetaMask/Runtime/netstandard2.1/MetaMask.SDK.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.VersionHandler">
      <HintPath>Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Dynamitey">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Dynamitey.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.EVM">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.EVM.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Web3">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Web3.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.UI">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Metamask">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Metamask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Unity">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Hex">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Hex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.Client">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.JsonRpc.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Accounts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Accounts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/Demigiant/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Unity.Metamask">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Unity.Metamask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Besu">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Besu.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Merkle">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Merkle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Signer.EIP712">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Signer.EIP712.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.GnosisSafe">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.GnosisSafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.JarResolver">
      <HintPath>Assets/ExternalDependencyManager/Editor/1.2.183/Google.JarResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets/PlayFlowCloud/Lib/ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Mud">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Mud.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Siwe">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Siwe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Util.Rest">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Util.Rest.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.VersionHandlerImpl">
      <HintPath>Assets/ExternalDependencyManager/Editor/1.2.183/Google.VersionHandlerImpl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NBitcoin">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/NBitcoin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.KeyStore">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.KeyStore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Contracts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
