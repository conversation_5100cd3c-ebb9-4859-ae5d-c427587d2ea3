{"dependencies": {"com.avaturn.webview": "https://github.com/avaturn/avaturn-unity-webview-sdk.git", "com.nethereum.unity": "https://github.com/Nethereum/Nethereum.Unity.git", "com.unity.2d.animation": "10.1.4", "com.unity.2d.aseprite": "1.1.5", "com.unity.2d.psdimporter": "9.0.3", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.spriteshape": "10.0.7", "com.unity.2d.tilemap": "1.0.0", "com.unity.ai.navigation": "2.0.8", "com.unity.animation.rigging": "1.3.0", "com.unity.burst": "1.8.24", "com.unity.collab-proxy": "2.5.2", "com.unity.feature.2d": "2.0.1", "com.unity.formats.fbx": "5.1.3", "com.unity.ide.rider": "3.0.34", "com.unity.ide.visualstudio": "2.0.22", "com.unity.ide.vscode": "1.2.5", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2", "com.unity.mobile.android-logcat": "1.4.4", "com.unity.multiplayer.center": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.recorder": "5.1.2", "com.unity.render-pipelines.universal": "17.0.3", "com.unity.render-pipelines.universal-config": "17.0.3", "com.unity.shadergraph": "17.0.3", "com.unity.test-framework": "1.4.5", "com.unity.timeline": "1.8.7", "com.unity.toolchain.macos-arm64-linux-x86_64": "2.0.4", "com.unity.ugui": "2.0.0", "com.unity.visualeffectgraph": "17.0.3", "com.unity.visualscripting": "1.9.5", "com.unity.xr.hands": "1.5.0", "com.unity.xr.interaction.toolkit": "3.0.7", "com.unity.xr.management": "4.5.0", "com.unity.xr.openxr": "1.13.2", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}, "scopedRegistries": [{"name": "OpenUPM", "url": "https://package.openupm.com", "scopes": ["com.atteneder"]}]}